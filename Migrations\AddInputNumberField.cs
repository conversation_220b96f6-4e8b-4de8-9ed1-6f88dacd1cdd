using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Data;
using System;
using System.Threading.Tasks;

namespace DriverManagementSystem.Migrations
{
    /// <summary>
    /// إضافة حقل رقم الإدخال إلى جدول الزيارات الميدانية
    /// </summary>
    public class AddInputNumberField
    {
        public static async Task ApplyAsync(ApplicationDbContext context)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 بدء إضافة حقل رقم الإدخال...");

                // التحقق من وجود العمود أولاً باستخدام SQL Server syntax
                var checkColumnSql = @"
                    SELECT COUNT(*)
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_NAME = 'FieldVisits'
                    AND COLUMN_NAME = 'InputNumber'";

                var columnExists = false;
                try
                {
                    // استخدام ExecuteScalarAsync للحصول على النتيجة
                    using var command = context.Database.GetDbConnection().CreateCommand();
                    command.CommandText = checkColumnSql;
                    await context.Database.OpenConnectionAsync();
                    var result = await command.ExecuteScalarAsync();
                    columnExists = Convert.ToInt32(result) > 0;
                    await context.Database.CloseConnectionAsync();
                }
                catch
                {
                    // إذا فشل الفحص، نحاول إضافة العمود مباشرة
                    columnExists = false;
                }

                if (!columnExists)
                {
                    // إضافة حقل رقم الإدخال
                    var addInputNumberSql = @"
                        ALTER TABLE FieldVisits
                        ADD InputNumber NVARCHAR(50) DEFAULT ''";

                    await context.Database.ExecuteSqlRawAsync(addInputNumberSql);
                    System.Diagnostics.Debug.WriteLine("✅ تم إضافة حقل InputNumber بنجاح");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("ℹ️ حقل InputNumber موجود مسبقاً");
                }

                System.Diagnostics.Debug.WriteLine("✅ تم تطبيق migration إضافة حقل رقم الإدخال بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تطبيق migration إضافة حقل رقم الإدخال: {ex.Message}");
                
                // لا نرمي الخطأ إذا كان العمود موجود مسبقاً
                if (!ex.Message.Contains("already exists") && 
                    !ex.Message.Contains("duplicate column name") &&
                    !ex.Message.Contains("Invalid column name"))
                {
                    throw;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("ℹ️ حقل InputNumber موجود مسبقاً - تم تجاهل الخطأ");
                }
            }
        }
    }
}
