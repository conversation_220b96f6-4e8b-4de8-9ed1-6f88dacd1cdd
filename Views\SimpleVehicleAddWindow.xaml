<Window x:Class="DriverManagementSystem.Views.SimpleVehicleAddWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة سيارة جديدة" 
        Height="600" Width="800"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="#F5F5F5"
        ResizeMode="CanResize">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2196F3" CornerRadius="8" Padding="20" Margin="0,0,0,20">
            <TextBlock Text="إضافة سيارة جديدة" 
                      FontSize="24" FontWeight="Bold" Foreground="White"
                      HorizontalAlignment="Center"/>
        </Border>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- Driver Information -->
                <Border Background="White" CornerRadius="8" Padding="20" BorderBrush="#E0E0E0" BorderThickness="1" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="بيانات السائق" FontSize="18" FontWeight="Bold"
                                  Foreground="#2196F3" Margin="0,0,0,15"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Row 1 -->
                            <StackPanel Grid.Row="0" Grid.Column="0" Margin="5">
                                <TextBlock Text="اسم السائق" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox x:Name="DriverNameTextBox" Height="35" FontSize="12"/>
                            </StackPanel>

                            <StackPanel Grid.Row="0" Grid.Column="1" Margin="5">
                                <TextBlock Text="رقم التلفون" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox x:Name="PhoneNumberTextBox" Height="35" FontSize="12"/>
                            </StackPanel>

                            <!-- Row 2 -->
                            <StackPanel Grid.Row="1" Grid.Column="0" Margin="5">
                                <TextBlock Text="رقم البطاقة الشخصية" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox x:Name="CardNumberTextBox" Height="35" FontSize="12"/>
                            </StackPanel>

                            <StackPanel Grid.Row="1" Grid.Column="1" Margin="5">
                                <TextBlock Text="نوع البطاقة" FontWeight="Bold" Margin="0,0,0,5"/>
                                <ComboBox x:Name="CardTypeComboBox" Height="35" FontSize="12">
                                    <ComboBoxItem Content="بطاقة شخصية"/>
                                    <ComboBoxItem Content="جواز سفر"/>
                                    <ComboBoxItem Content="بطاقة عائلية"/>
                                    <ComboBoxItem Content="بطاقة عسكرية"/>
                                </ComboBox>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Vehicle Information -->
                <Border Background="White" CornerRadius="8" Padding="20" BorderBrush="#E0E0E0" BorderThickness="1" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="بيانات السيارة" FontSize="18" FontWeight="Bold"
                                  Foreground="#2196F3" Margin="0,0,0,15"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Row 1 -->
                            <StackPanel Grid.Row="0" Grid.Column="0" Margin="5">
                                <TextBlock Text="نوع السيارة" FontWeight="Bold" Margin="0,0,0,5"/>
                                <ComboBox x:Name="VehicleTypeComboBox" Height="35" FontSize="12">
                                    <ComboBoxItem Content="فورشنال"/>
                                    <ComboBoxItem Content="هيلوكس"/>
                                    <ComboBoxItem Content="برادو"/>
                                    <ComboBoxItem Content="كنتر"/>
                                    <ComboBoxItem Content="حافلة"/>
                                </ComboBox>
                            </StackPanel>

                            <StackPanel Grid.Row="0" Grid.Column="1" Margin="5">
                                <TextBlock Text="رقم السيارة" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox x:Name="VehicleNumberTextBox" Height="35" FontSize="12"/>
                            </StackPanel>

                            <!-- Row 2 -->
                            <StackPanel Grid.Row="1" Grid.Column="0" Margin="5">
                                <TextBlock Text="موديل السيارة" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox x:Name="VehicleModelTextBox" Height="35" FontSize="12"/>
                            </StackPanel>

                            <StackPanel Grid.Row="1" Grid.Column="1" Margin="5">
                                <TextBlock Text="سعة السيارة" FontWeight="Bold" Margin="0,0,0,5"/>
                                <ComboBox x:Name="VehicleCapacityComboBox" Height="35" FontSize="12">
                                    <ComboBoxItem Content="4 بسطون"/>
                                    <ComboBoxItem Content="6 بسطون"/>
                                </ComboBox>
                            </StackPanel>

                            <!-- Row 3 -->
                            <StackPanel Grid.Row="2" Grid.Column="0" Margin="5">
                                <TextBlock Text="لون السيارة" FontWeight="Bold" Margin="0,0,0,5"/>
                                <ComboBox x:Name="VehicleColorComboBox" Height="35" FontSize="12">
                                    <ComboBoxItem Content="أبيض"/>
                                    <ComboBoxItem Content="أسود"/>
                                    <ComboBoxItem Content="فضي"/>
                                    <ComboBoxItem Content="رمادي"/>
                                    <ComboBoxItem Content="أزرق"/>
                                    <ComboBoxItem Content="أحمر"/>
                                </ComboBox>
                            </StackPanel>

                            <StackPanel Grid.Row="2" Grid.Column="1" Margin="5">
                                <TextBlock Text="رقم الرخصة" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox x:Name="LicenseNumberTextBox" Height="35" FontSize="12"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Notes -->
                <Border Background="White" CornerRadius="8" Padding="20" BorderBrush="#E0E0E0" BorderThickness="1">
                    <StackPanel>
                        <TextBlock Text="ملاحظات" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="NotesTextBox" Height="80" FontSize="12" 
                                TextWrapping="Wrap" AcceptsReturn="True"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <!-- Action Buttons -->
        <Border Grid.Row="2" Background="White" CornerRadius="8" Padding="15" Margin="0,20,0,0"
                BorderBrush="#E0E0E0" BorderThickness="1">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="SaveButton" Content="💾 حفظ" 
                       Height="40" MinWidth="120" Margin="10" FontSize="14" FontWeight="Bold"
                       Background="#4CAF50" Foreground="White" BorderBrush="Transparent"
                       Click="SaveButton_Click"/>

                <Button x:Name="ClearButton" Content="🗑️ مسح" 
                       Height="40" MinWidth="120" Margin="10" FontSize="14" FontWeight="Bold"
                       Background="#FF9800" Foreground="White" BorderBrush="Transparent"
                       Click="ClearButton_Click"/>

                <Button x:Name="CancelButton" Content="❌ إلغاء" 
                       Height="40" MinWidth="120" Margin="10" FontSize="14" FontWeight="Bold"
                       Background="#F44336" Foreground="White" BorderBrush="Transparent"
                       Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
