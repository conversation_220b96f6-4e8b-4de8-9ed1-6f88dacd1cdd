using System;
using System.Windows;
using System.Windows.Controls;
using DriverManagementSystem.Models;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.Views
{
    public partial class SimpleVehicleAddWindow : Window
    {
        private readonly DatabaseService _dataService;

        public SimpleVehicleAddWindow()
        {
            try
            {
                InitializeComponent();
                _dataService = new DatabaseService();
                
                // Set default selections
                CardTypeComboBox.SelectedIndex = 0;
                VehicleTypeComboBox.SelectedIndex = 0;
                VehicleCapacityComboBox.SelectedIndex = 0;
                VehicleColorComboBox.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة النافذة: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Validate required fields
                if (string.IsNullOrWhiteSpace(DriverNameTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم السائق", "تحذير", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    DriverNameTextBox.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(VehicleNumberTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال رقم السيارة", "تحذير", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    VehicleNumberTextBox.Focus();
                    return;
                }

                // Generate driver code
                var driverCode = await GenerateDriverCodeAsync();

                // Create new driver
                var newDriver = new Driver
                {
                    DriverCode = driverCode,
                    Name = DriverNameTextBox.Text.Trim(),
                    PhoneNumber = PhoneNumberTextBox.Text.Trim(),
                    CardNumber = CardNumberTextBox.Text.Trim(),
                    CardType = (CardTypeComboBox.SelectedItem as ComboBoxItem)?.Content?.ToString() ?? "بطاقة شخصية",
                    CardIssuePlace = "غير محدد",
                    CardIssueDate = DateTime.Now,
                    VehicleType = (VehicleTypeComboBox.SelectedItem as ComboBoxItem)?.Content?.ToString() ?? "فورشنال",
                    VehicleNumber = VehicleNumberTextBox.Text.Trim(),
                    VehicleModel = VehicleModelTextBox.Text.Trim(),
                    VehicleCapacity = (VehicleCapacityComboBox.SelectedItem as ComboBoxItem)?.Content?.ToString() ?? "4 بسطون",
                    VehicleColor = (VehicleColorComboBox.SelectedItem as ComboBoxItem)?.Content?.ToString() ?? "أبيض",
                    LicenseNumber = LicenseNumberTextBox.Text.Trim(),
                    LicenseIssueDate = DateTime.Now,
                    Notes = NotesTextBox.Text.Trim(),
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                // Save to database
                var success = await _dataService.AddDriverAsync(newDriver);

                if (success)
                {
                    // Create associated vehicle
                    var vehicle = new Vehicle
                    {
                        OwnerName = newDriver.Name,
                        IdCardNumber = newDriver.CardNumber,
                        VehicleType = newDriver.VehicleType,
                        PlateNumber = newDriver.VehicleNumber,
                        DriverCode = newDriver.DriverCode,
                        SectorId = 1, // Default sector
                        SectorName = "غير محدد",
                        CreatedAt = DateTime.Now,
                        IsActive = true
                    };

                    await _dataService.AddVehicleAsync(vehicle);

                    MessageBox.Show($"✅ تم إضافة السائق والسيارة بنجاح!\n\nكود السائق: {driverCode}\nاسم السائق: {newDriver.Name}", 
                                  "نجح الحفظ", MessageBoxButton.OK, MessageBoxImage.Information);

                    DialogResult = true;
                    Close();
                }
                else
                {
                    MessageBox.Show("❌ فشل في حفظ البيانات", "خطأ", 
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في حفظ البيانات: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Clear all fields
                DriverNameTextBox.Clear();
                PhoneNumberTextBox.Clear();
                CardNumberTextBox.Clear();
                VehicleNumberTextBox.Clear();
                VehicleModelTextBox.Clear();
                LicenseNumberTextBox.Clear();
                NotesTextBox.Clear();

                // Reset combo boxes
                CardTypeComboBox.SelectedIndex = 0;
                VehicleTypeComboBox.SelectedIndex = 0;
                VehicleCapacityComboBox.SelectedIndex = 0;
                VehicleColorComboBox.SelectedIndex = 0;

                DriverNameTextBox.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في مسح البيانات: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private async System.Threading.Tasks.Task<string> GenerateDriverCodeAsync()
        {
            try
            {
                var drivers = await _dataService.GetDriversAsync();
                var maxCode = 0;

                foreach (var driver in drivers)
                {
                    if (!string.IsNullOrEmpty(driver.DriverCode) && 
                        int.TryParse(driver.DriverCode, out int code))
                    {
                        if (code > maxCode)
                            maxCode = code;
                    }
                }

                return (maxCode + 1).ToString("D3"); // Format as 3-digit number with leading zeros
            }
            catch
            {
                return "001"; // Default if error occurs
            }
        }
    }
}
