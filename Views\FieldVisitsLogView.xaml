<UserControl x:Class="DriverManagementSystem.Views.FieldVisitsLogView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:DriverManagementSystem.Views"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Resources/Colors.xaml"/>
                <ResourceDictionary Source="../Resources/Styles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid Background="#F8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Professional Header -->
        <Border Grid.Row="0" CornerRadius="0,0,15,15" Margin="0,0,0,20">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#FF074C51" Offset="0"/>
                    <GradientStop Color="#FF1F9DC0" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <Border.Effect>
                <DropShadowEffect Color="Black" Direction="270" ShadowDepth="5" Opacity="0.3" BlurRadius="10"/>
            </Border.Effect>

            <Grid Margin="30,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Logo and Title -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <Border Background="White" CornerRadius="25" Width="50" Height="50" Margin="0,0,15,0">
                        <TextBlock Text="📋" FontSize="24" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <StackPanel VerticalAlignment="Center">
                        <TextBlock Text="سجل الزيارات الميدانية"
                                  FontSize="26" FontWeight="Bold" Foreground="White"/>
                        <TextBlock Text="عرض وإدارة جميع الزيارات المسجلة في النظام"
                                  FontSize="14" Foreground="#C8E6C9" Margin="0,5,0,0"/>
                    </StackPanel>
                </StackPanel>

                <!-- Statistics -->
                <StackPanel Grid.Column="1" HorizontalAlignment="Center" VerticalAlignment="Center">
                    <TextBlock Text="{Binding TotalVisits, StringFormat='إجمالي الزيارات: {0}'}"
                              FontSize="16" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding ActiveVisits, StringFormat='الزيارات النشطة: {0}'}"
                              FontSize="14" Foreground="#C8E6C9" HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- Search -->
                <StackPanel Grid.Column="2" VerticalAlignment="Center">
                    <TextBlock Text="🔍 البحث السريع" FontSize="14" FontWeight="Bold" Foreground="White" Margin="0,0,0,5"/>
                    <TextBox Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                            Width="200" Height="35" FontSize="12" 
                            Background="White" BorderBrush="#4CAF50" BorderThickness="2"
                            Padding="10,0" VerticalContentAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="20,0,20,20">
            <Border Background="White" CornerRadius="15" Padding="25">
                <Border.Effect>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="8" Opacity="0.15" BlurRadius="20"/>
                </Border.Effect>

                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Filter Bar -->
                    <Border Grid.Row="0" Background="#F1F8E9" CornerRadius="10" Padding="15" Margin="0,0,0,20">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- Filter Label -->
                            <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,20,0">
                                <TextBlock Text="🎯" FontSize="16" Margin="0,0,8,0"/>
                                <TextBlock Text="تصفية النتائج:" FontSize="14" FontWeight="Bold" Foreground="#2E7D32"/>
                            </StackPanel>

                            <!-- Sector Filter -->
                            <StackPanel Grid.Column="1" Margin="0,0,15,0">
                                <TextBlock Text="القطاع:" FontSize="12" FontWeight="Bold" Foreground="#2E7D32" Margin="0,0,0,5"/>
                                <ComboBox ItemsSource="{Binding Sectors}" SelectedItem="{Binding SelectedSectorFilter}"
                                         Height="32" FontSize="12" BorderBrush="#4CAF50" BorderThickness="1">
                                    <ComboBox.ItemTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding Name}" FontSize="12"/>
                                        </DataTemplate>
                                    </ComboBox.ItemTemplate>
                                </ComboBox>
                            </StackPanel>

                            <!-- Date Filter -->
                            <StackPanel Grid.Column="2" Margin="0,0,15,0">
                                <TextBlock Text="التاريخ:" FontSize="12" FontWeight="Bold" Foreground="#2E7D32" Margin="0,0,0,5"/>
                                <DatePicker SelectedDate="{Binding SelectedDateFilter}"
                                           Height="32" FontSize="12" BorderBrush="#4CAF50" BorderThickness="1"/>
                            </StackPanel>

                            <!-- Action Buttons -->
                            <WrapPanel Grid.Column="3" Orientation="Horizontal" VerticalAlignment="Bottom">
                                <Button Content="🔄 تحديث البيانات" Height="32" FontSize="12" FontWeight="Bold"
                                       Background="#4CAF50" Foreground="White" BorderThickness="0" Margin="0,0,5,0"
                                       Command="{Binding RefreshDataCommand}"/>
                                <Button Content="⚡ إعادة تحميل قوي" Height="32" FontSize="12" FontWeight="Bold"
                                       Background="#E91E63" Foreground="White" BorderThickness="0" Margin="0,0,5,0"
                                       Command="{Binding ForceRefreshDataCommand}"
                                       ToolTip="إعادة تحميل جميع البيانات من قاعدة البيانات"/>
                                <Button Content="📊 تقرير الرسائل" Height="32" FontSize="12" FontWeight="Bold"
                                       Background="#2196F3" Foreground="White" BorderThickness="0" Margin="0,0,5,0"
                                       Command="{Binding ViewMessagesReportCommand}"/>
                                <Button Content="🗑️ مسح التصفية" Height="32" FontSize="12" FontWeight="Bold"
                                       Background="#FF9800" Foreground="White" BorderThickness="0"
                                       Command="{Binding ClearFiltersCommand}"/>
                            </WrapPanel>
                        </Grid>
                    </Border>

                    <!-- Visits DataGrid -->
                    <DataGrid Grid.Row="1" ItemsSource="{Binding FilteredVisits}"
                             SelectedItem="{Binding SelectedVisit}"
                             AutoGenerateColumns="False" CanUserAddRows="False"
                             CanUserDeleteRows="False" IsReadOnly="True"
                             GridLinesVisibility="None" HeadersVisibility="Column"
                             Background="White" RowBackground="#FAFAFA"
                             AlternatingRowBackground="#F5F5F5"
                             FontSize="12" RowHeight="60"
                             FrozenColumnCount="3"
                             CanUserSortColumns="True"
                             EnableRowVirtualization="True"
                             EnableColumnVirtualization="False"
                             ScrollViewer.CanContentScroll="True"
                             ScrollViewer.VerticalScrollBarVisibility="Auto"
                             ScrollViewer.HorizontalScrollBarVisibility="Auto"
                             VirtualizingPanel.IsVirtualizing="True"
                             VirtualizingPanel.VirtualizationMode="Recycling">

                        <DataGrid.ColumnHeaderStyle>
                            <Style TargetType="DataGridColumnHeader">
                                <Setter Property="Background">
                                    <Setter.Value>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                            <GradientStop Color="#4CAF50" Offset="0"/>
                                            <GradientStop Color="#2E7D32" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Setter.Value>
                                </Setter>
                                <Setter Property="Foreground" Value="White"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                                <Setter Property="FontSize" Value="13"/>
                                <Setter Property="Padding" Value="10,12"/>
                                <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                <Setter Property="VerticalContentAlignment" Value="Center"/>
                                <Setter Property="BorderBrush" Value="#1B5E20"/>
                                <Setter Property="BorderThickness" Value="0,0,1,3"/>
                                <Setter Property="Height" Value="50"/>
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="DataGridColumnHeader">
                                            <Border Background="{TemplateBinding Background}"
                                                   BorderBrush="{TemplateBinding BorderBrush}"
                                                   BorderThickness="{TemplateBinding BorderThickness}"
                                                   Padding="{TemplateBinding Padding}"
                                                   CornerRadius="0">
                                                <Grid>
                                                    <!-- خلفية إضافية للتأكيد -->
                                                    <Rectangle Fill="#1B5E20" Opacity="0.3" Height="2" VerticalAlignment="Bottom"/>
                                                    <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                                    VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                                                    TextBlock.FontWeight="Bold"
                                                                    TextBlock.FontSize="13"
                                                                    TextBlock.Foreground="White"/>
                                                </Grid>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background">
                                                        <Setter.Value>
                                                            <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                                                <GradientStop Color="#66BB6A" Offset="0"/>
                                                                <GradientStop Color="#388E3C" Offset="1"/>
                                                            </LinearGradientBrush>
                                                        </Setter.Value>
                                                    </Setter>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </DataGrid.ColumnHeaderStyle>

                        <!-- تنسيق الصفوف المحددة -->
                        <DataGrid.RowStyle>
                            <Style TargetType="DataGridRow">
                                <Style.Triggers>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter Property="Background" Value="#2196F3"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                    </Trigger>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#E3F2FD"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </DataGrid.RowStyle>

                        <DataGrid.Columns>

                            <!-- Actions -->
                            <DataGridTemplateColumn Header="⚙️ الإجراءات" Width="280">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <!-- Message Button - First and Larger -->
                                            <Button Content="📱 رسالة" Width="50" Height="35" Margin="2" FontSize="10"
                                                   Background="#FF5722" Foreground="White" BorderThickness="0"
                                                   FontWeight="Bold"
                                                   ToolTip="إنشاء رسالة للسائق"
                                                   Command="{Binding DataContext.CreateMessageCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                   CommandParameter="{Binding}">
                                                <Button.Style>
                                                    <Style TargetType="Button">
                                                        <Setter Property="Cursor" Value="Hand"/>
                                                        <Style.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter Property="Background" Value="#E64A19"/>
                                                            </Trigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </Button.Style>
                                            </Button>

                                            <!-- Report Button - Second and Larger -->
                                            <Button Content="📄 تقرير" Width="50" Height="35" Margin="2" FontSize="10"
                                                   Background="#2E7D32" Foreground="White" BorderThickness="0"
                                                   FontWeight="Bold"
                                                   ToolTip="إنشاء محضر استخراج عروض الأسعار"
                                                   Command="{Binding DataContext.CreateReportCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                   CommandParameter="{Binding}">
                                                <Button.Style>
                                                    <Style TargetType="Button">
                                                        <Setter Property="Cursor" Value="Hand"/>
                                                        <Style.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter Property="Background" Value="#1B5E20"/>
                                                            </Trigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </Button.Style>
                                            </Button>

                                            <!-- View Details Button -->
                                            <Button Content="👁️" Width="30" Height="30" Margin="1" FontSize="12"
                                                   Background="#2196F3" Foreground="White" BorderThickness="0"
                                                   ToolTip="عرض التفاصيل"
                                                   Command="{Binding DataContext.ViewDetailsCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                   CommandParameter="{Binding}"/>

                                            <!-- Edit Button -->
                                            <Button Content="✏️" Width="30" Height="30" Margin="1" FontSize="12"
                                                   Background="#FF9800" Foreground="White" BorderThickness="0"
                                                   ToolTip="تعديل"
                                                   Command="{Binding DataContext.EditVisitCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                   CommandParameter="{Binding}"/>

                                            <!-- Delete Button -->
                                            <Button Content="🗑️" Width="30" Height="30" Margin="1" FontSize="12"
                                                   Background="#F44336" Foreground="White" BorderThickness="0"
                                                   ToolTip="حذف"
                                                   Command="{Binding DataContext.DeleteVisitCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                   CommandParameter="{Binding}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- Driver Contract -->
                            <DataGridTextColumn Header="📋 عقد السائق" Binding="{Binding DriverContract}" Width="120">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="Foreground" Value="#1976D2"/>
                                        <Setter Property="Padding" Value="8,4"/>
                                        <Setter Property="Margin" Value="2"/>
                                        <!-- إزالة Background لتمكين تنسيق التحديد -->
                                        <Style.Triggers>
                                            <!-- تنسيق خاص عند التحديد -->
                                            <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=DataGridRow}, Path=IsSelected}" Value="True">
                                                <Setter Property="Foreground" Value="White"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <!-- Input Number -->
                            <DataGridTextColumn Header="🔢 رقم الإدخال" Binding="{Binding InputNumber}" Width="90">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="Foreground" Value="#9C27B0"/>
                                        <Setter Property="Padding" Value="8,4"/>
                                        <Setter Property="Margin" Value="2"/>
                                        <Style.Triggers>
                                            <!-- تنسيق خاص عند التحديد -->
                                            <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=DataGridRow}, Path=IsSelected}" Value="True">
                                                <Setter Property="Foreground" Value="White"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <!-- Visit Number -->
                            <DataGridTextColumn Header="🔢 رقم الزيارة" Binding="{Binding VisitNumber}" Width="100">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="Foreground" Value="#2E7D32"/>
                                        <Setter Property="Padding" Value="8,4"/>
                                        <Setter Property="Margin" Value="2"/>
                                        <Style.Triggers>
                                            <!-- تنسيق خاص عند التحديد -->
                                            <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=DataGridRow}, Path=IsSelected}" Value="True">
                                                <Setter Property="Foreground" Value="White"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <!-- Add Date -->
                            <DataGridTextColumn Header="📅 تاريخ الإضافة" Binding="{Binding AddDate, StringFormat=dd/MM/yyyy}" Width="110">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <!-- Departure Date -->
                            <DataGridTextColumn Header="🚀 تاريخ النزول" Binding="{Binding DepartureDate, StringFormat=dd/MM/yyyy}" Width="110">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <!-- Return Date -->
                            <DataGridTextColumn Header="🏠 تاريخ العودة" Binding="{Binding ReturnDate, StringFormat=dd/MM/yyyy}" Width="110">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <!-- Days Count -->
                            <DataGridTextColumn Header="⏱️ عدد الأيام" Binding="{Binding DaysCount}" Width="80">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="Foreground" Value="#FF9800"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <!-- Sector -->
                            <DataGridTextColumn Header="🏢 القطاع" Binding="{Binding SectorName}" Width="150">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="FontWeight" Value="SemiBold"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <!-- Visitors Count -->
                            <DataGridTextColumn Header="👥 عدد المشاركين" Binding="{Binding VisitorsCount}" Width="100">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="Foreground" Value="#9C27B0"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <!-- Mission Purpose -->
                            <DataGridTextColumn Header="🎯 مهمة النزول" Binding="{Binding MissionPurpose}" Width="200">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="TextWrapping" Value="Wrap"/>
                                        <Setter Property="Margin" Value="5"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <!-- Projects -->
                            <DataGridTemplateColumn Header="📁 المشاريع" Width="200">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <ScrollViewer VerticalScrollBarVisibility="Auto" MaxHeight="50">
                                            <ItemsControl ItemsSource="{Binding ProjectNames}">
                                                <ItemsControl.ItemTemplate>
                                                    <DataTemplate>
                                                        <Border Background="#FFF3E0" CornerRadius="3" Padding="4,2" Margin="1">
                                                            <TextBlock Text="{Binding}" FontSize="10"
                                                                      TextWrapping="Wrap" Foreground="#FF5722"/>
                                                        </Border>
                                                    </DataTemplate>
                                                </ItemsControl.ItemTemplate>
                                            </ItemsControl>
                                        </ScrollViewer>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- Visitors -->
                            <DataGridTemplateColumn Header="👨‍💼 القائمين بالزيارة" Width="200">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <ScrollViewer VerticalScrollBarVisibility="Auto" MaxHeight="50">
                                            <ItemsControl ItemsSource="{Binding VisitorNames}">
                                                <ItemsControl.ItemTemplate>
                                                    <DataTemplate>
                                                        <Border Background="#E1F5FE" CornerRadius="3" Padding="4,2" Margin="1">
                                                            <TextBlock Text="{Binding}" FontSize="10"
                                                                      TextWrapping="Wrap" Foreground="#0277BD"/>
                                                        </Border>
                                                    </DataTemplate>
                                                </ItemsControl.ItemTemplate>
                                            </ItemsControl>
                                        </ScrollViewer>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- Itinerary -->
                            <DataGridTemplateColumn Header="🗺️ خط السير" Width="200">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <ScrollViewer VerticalScrollBarVisibility="Auto" MaxHeight="50">
                                            <StackPanel>
                                                <!-- خط السير النصي الجديد -->
                                                <Border Background="#E8F5E8" CornerRadius="3" Padding="4,2" Margin="1">
                                                    <TextBlock Text="{Binding ItineraryText}" FontSize="10"
                                                              TextWrapping="Wrap" Foreground="#2E7D32"
                                                              Visibility="{Binding ItineraryText, Converter={StaticResource StringToVisibilityConverter}}"/>
                                                </Border>
                                                <!-- خط السير القديم (قائمة) -->
                                                <ItemsControl ItemsSource="{Binding Itinerary}">
                                                    <ItemsControl.ItemTemplate>
                                                        <DataTemplate>
                                                            <Border Background="#E8F5E8" CornerRadius="3" Padding="4,2" Margin="1">
                                                                <TextBlock Text="{Binding}" FontSize="10"
                                                                          TextWrapping="Wrap" Foreground="#2E7D32"/>
                                                            </Border>
                                                        </DataTemplate>
                                                    </ItemsControl.ItemTemplate>
                                                </ItemsControl>
                                            </StackPanel>
                                        </ScrollViewer>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- Notes -->
                            <DataGridTemplateColumn Header="📝 ملاحظات" Width="200">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <ScrollViewer VerticalScrollBarVisibility="Auto" MaxHeight="50">
                                            <Border Background="#FFF8E1" CornerRadius="3" Padding="4,2" Margin="1">
                                                <TextBlock Text="{Binding Notes}" FontSize="10"
                                                          TextWrapping="Wrap" Foreground="#F57F17"/>
                                            </Border>
                                        </ScrollViewer>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>


                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </Border>
        </ScrollViewer>
    </Grid>
</UserControl>
