﻿#pragma checksum "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "AA3024CFABB62FC40ED2EB0269F7FCB91D4EB2D7"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DriverManagementSystem;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DriverManagementSystem.Views {
    
    
    /// <summary>
    /// ProfessionalDriverManagementWindow
    /// </summary>
    public partial class ProfessionalDriverManagementWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 143 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 157 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        
        #line 257 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ForshanalFilter;
        
        #line default
        #line hidden
        
        
        #line 266 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox KanterFilter;
        
        #line default
        #line hidden
        
        
        #line 275 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox HiluxFilter;
        
        #line default
        #line hidden
        
        
        #line 284 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox BusFilter;
        
        #line default
        #line hidden
        
        
        #line 293 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox PradoFilter;
        
        #line default
        #line hidden
        
        
        #line 357 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid DriversDataGrid;
        
        #line default
        #line hidden
        
        
        #line 436 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel DriverDetailsPanel;
        
        #line default
        #line hidden
        
        
        #line 522 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NoSelectionMessage;
        
        #line default
        #line hidden
        
        
        #line 559 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ConfirmSelectionButton;
        
        #line default
        #line hidden
        
        
        #line 567 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SFDSystem;V2.0.0.0;component/views/professionaldrivermanagementwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            
            #line 150 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearSearch_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 157 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.ForshanalFilter = ((System.Windows.Controls.CheckBox)(target));
            
            #line 258 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.ForshanalFilter.Checked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            
            #line 258 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.ForshanalFilter.Unchecked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 5:
            this.KanterFilter = ((System.Windows.Controls.CheckBox)(target));
            
            #line 267 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.KanterFilter.Checked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            
            #line 267 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.KanterFilter.Unchecked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 6:
            this.HiluxFilter = ((System.Windows.Controls.CheckBox)(target));
            
            #line 276 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.HiluxFilter.Checked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            
            #line 276 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.HiluxFilter.Unchecked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 7:
            this.BusFilter = ((System.Windows.Controls.CheckBox)(target));
            
            #line 285 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.BusFilter.Checked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            
            #line 285 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.BusFilter.Unchecked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 8:
            this.PradoFilter = ((System.Windows.Controls.CheckBox)(target));
            
            #line 294 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.PradoFilter.Checked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            
            #line 294 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.PradoFilter.Unchecked += new System.Windows.RoutedEventHandler(this.VehicleFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 308 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SelectAllDrivers_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 316 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearSelection_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 324 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RefreshData_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.DriversDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 363 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.DriversDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.DriversDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 13:
            this.DriverDetailsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 14:
            
            #line 515 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CallDriver_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 517 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.MessageDriver_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.NoSelectionMessage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.ConfirmSelectionButton = ((System.Windows.Controls.Button)(target));
            
            #line 559 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.ConfirmSelectionButton.Click += new System.Windows.RoutedEventHandler(this.ConfirmSelectionButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 567 "..\..\..\..\Views\ProfessionalDriverManagementWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

